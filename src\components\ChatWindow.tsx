import React, { useState, useRef, useEffect } from 'react';
import { Send, MessageSquare, Plus, Menu, X } from 'lucide-react';
import { useChat } from '../hooks/useChat';

const ChatWindow: React.FC = () => {
  const [input, setInput] = useState('');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const {
    messages,
    conversations,
    activeConversationId,
    isLoading,
    sendMessage,
    createNewConversation,
    selectConversation
  } = useChat();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const message = input.trim();
    setInput('');
    await sendMessage(message);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleNewConversation = () => {
    createNewConversation();
    setSidebarOpen(false);
  };

  return (
    <div className="h-screen flex bg-black">
      {/* Left Sidebar - Conversations */}
      <div className={`${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} fixed inset-y-0 left-0 z-50 w-80 bg-black/95 backdrop-blur-sm border-r border-white/10 transition-transform duration-300 lg:translate-x-0 lg:static lg:inset-0`}>
        {/* Logo in top-left */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <h1
            className="text-2xl font-bold text-transparent"
            style={{
              fontFamily: "'Inter', 'Space Grotesk', 'Satoshi', sans-serif",
              fontWeight: 700,
              color: 'transparent',
              WebkitTextStroke: '1px white',
              letterSpacing: 'normal',
              textShadow: '0 0 10px rgba(255, 255, 255, 0.1)'
            }}
          >
            */now.
          </h1>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden p-2 rounded-lg hover:bg-white/10 transition-colors"
          >
            <X className="w-5 h-5 text-white" />
          </button>
        </div>

        {/* New Conversation Button */}
        <div className="p-4">
          <button
            onClick={handleNewConversation}
            className="w-full flex items-center gap-3 p-3 rounded-xl bg-white/10 hover:bg-white/20 border border-white/20 text-white transition-all duration-200"
          >
            <Plus className="w-5 h-5" />
            <span>New conversation</span>
          </button>
        </div>

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto px-4 pb-4">
          <div className="space-y-2">
            {conversations.map((conversation) => (
              <button
                key={conversation.id}
                onClick={() => {
                  selectConversation(conversation.id);
                  setSidebarOpen(false);
                }}
                className={`w-full text-left p-3 rounded-xl transition-all duration-200 ${
                  activeConversationId === conversation.id
                    ? 'bg-white/20 border border-white/30'
                    : 'hover:bg-white/10 border border-transparent'
                }`}
              >
                <div className="text-white font-medium text-sm truncate">
                  {conversation.title}
                </div>
                <div className="text-white/60 text-xs mt-1 truncate">
                  {conversation.lastMessage || 'No messages yet'}
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Right Pane - Main Chat Area */}
      <div className="flex-1 flex flex-col lg:ml-0">
        {/* Mobile Header */}
        <div className="lg:hidden flex items-center justify-between p-4 border-b border-white/10">
          <button
            onClick={() => setSidebarOpen(true)}
            className="p-2 rounded-lg hover:bg-white/10 transition-colors"
          >
            <Menu className="w-5 h-5 text-white" />
          </button>
          <h1
            className="text-xl font-bold text-transparent"
            style={{
              fontFamily: "'Inter', 'Space Grotesk', 'Satoshi', sans-serif",
              fontWeight: 700,
              color: 'transparent',
              WebkitTextStroke: '1px white',
              letterSpacing: 'normal',
              textShadow: '0 0 10px rgba(255, 255, 255, 0.1)'
            }}
          >
            */now.
          </h1>
          <div className="w-9"></div> {/* Spacer for centering */}
        </div>

        {/* Chat Messages Area */}
        <div className="flex-1 flex flex-col">
          <div className="flex-1 overflow-y-auto chat-messages">
            {messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center space-y-6 p-8">
                {/* Center-aligned landing page title */}
                <div className="space-y-4">
                  <h1
                    className="text-5xl lg:text-6xl font-bold text-transparent"
                    style={{
                      fontFamily: "'Inter', 'Space Grotesk', 'Satoshi', sans-serif",
                      fontWeight: 700,
                      color: 'transparent',
                      WebkitTextStroke: '1px white',
                      letterSpacing: 'normal',
                      textShadow: '0 0 20px rgba(255, 255, 255, 0.1)'
                    }}
                  >
                    */now.
                  </h1>
                  <p className="text-white/70 text-lg max-w-md mx-auto">
                    Your next move. Right now.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="p-4 rounded-full bg-white/5 backdrop-blur-sm border border-white/10">
                    <MessageSquare className="w-8 h-8 text-white/60" />
                  </div>
                  <div className="space-y-2">
                    <h2 className="text-xl font-semibold text-white">Start a conversation</h2>
                    <p className="text-white/60">Ask anything to get started</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="max-w-4xl mx-auto w-full px-6 py-6">
                <div className="space-y-6">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[85%] p-4 rounded-2xl backdrop-blur-sm border ${
                          message.role === 'user'
                            ? 'bg-white/10 border-white/20 text-white shadow-lg'
                            : 'bg-white/5 border-white/10 text-white shadow-lg'
                        }`}
                        style={{
                          // Enhanced text visibility for result summary
                          textShadow: message.role === 'assistant' ? '0 1px 2px rgba(0, 0, 0, 0.5)' : 'none'
                        }}
                      >
                        <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                        <div className="text-xs text-white/50 mt-3 font-medium">
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Loading indicator */}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="max-w-[85%] p-4 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 shadow-lg">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-white/50 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-white/50 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-white/50 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className="flex-shrink-0 p-6 border-t border-white/10">
            <div className="max-w-4xl mx-auto">
              <form onSubmit={handleSubmit} className="relative">
                <div className="relative flex items-center">
                  <input
                    type="text"
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Ask anything..."
                    disabled={isLoading}
                    className="w-full px-6 py-4 pr-14 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 transition-all duration-200 shadow-lg"
                  />
                  <button
                    type="submit"
                    disabled={!input.trim() || isLoading}
                    className="absolute right-2 p-2 rounded-xl bg-white/10 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 border border-white/20"
                  >
                    <Send className="w-5 h-5 text-white" />
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* Overlay for mobile sidebar */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default ChatWindow;
