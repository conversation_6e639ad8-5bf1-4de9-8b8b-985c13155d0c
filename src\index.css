@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure smooth font rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  background: linear-gradient(135deg, #18181b 0%, #171717 50%, #000000 100%);
  min-height: 100vh;
}

/* Selection styling */
::selection {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Focus styles */
*:focus {
  outline: 2px solid rgba(255, 255, 255, 0.3);
  outline-offset: 2px;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* Logo text styling - now handled inline */

/* Fast, minimal animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-from-bottom {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}

@keyframes float-delayed {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

@keyframes float-gentle {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-2px); }
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(12px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fade-in-delayed {
  0% { opacity: 0; transform: translateY(12px); }
  40% { opacity: 0; transform: translateY(12px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes fade-in-delayed-2 {
  0% { opacity: 0; transform: translateY(12px); }
  50% { opacity: 0; transform: translateY(12px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* Removed pulse-glow animation for minimal styling */

.animate-in {
  animation: slide-in-from-bottom 0.15s ease-out;
}

.slide-in-from-bottom-2 {
  animation: slide-in-from-bottom 0.15s ease-out;
}

.float {
  animation: float 8s ease-in-out infinite;
}

.float-delayed {
  animation: float-delayed 10s ease-in-out infinite;
}

.float-gentle {
  animation: float-gentle 15s ease-in-out infinite;
}

.fade-in {
  animation: fade-in 0.3s ease-out;
}

.fade-in-delayed {
  animation: fade-in-delayed 0.5s ease-out;
}

.fade-in-delayed-2 {
  animation: fade-in-delayed-2 0.7s ease-out;
}

/* Removed pulse-glow class for minimal styling */

/* Enhanced scrollbar for chat messages */
.chat-messages::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.2), rgba(156, 163, 175, 0.1));
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.4), rgba(156, 163, 175, 0.2));
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

/* Enhanced selection styling */
::selection {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.2), rgba(156, 163, 175, 0.1));
  color: white;
}

/* Enhanced focus styles */
*:focus {
  outline: 2px solid rgba(255, 255, 255, 0.3);
  outline-offset: 2px;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}



