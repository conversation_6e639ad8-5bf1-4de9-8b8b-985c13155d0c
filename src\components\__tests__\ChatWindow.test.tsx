import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import ChatWindow from '../ChatWindow';

describe('ChatWindow', () => {
  it('renders the chat window with initial state', () => {
    render(<ChatWindow />);
    
    // Check for initial empty state message
    expect(screen.getByText('Start a conversation')).toBeInTheDocument();
    expect(screen.getByText('Ask anything to get started')).toBeInTheDocument();
    
    // Check for input field
    expect(screen.getByPlaceholderText('Ask anything...')).toBeInTheDocument();
    
    // Check for send button
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('allows user to send a message', async () => {
    render(<ChatWindow />);
    
    const input = screen.getByPlaceholderText('Ask anything...');
    const sendButton = screen.getByRole('button');
    
    // Type a message
    fireEvent.change(input, { target: { value: 'Hello, world!' } });
    
    // Send the message
    fireEvent.click(sendButton);
    
    // Check that the user message appears
    expect(screen.getByText('Hello, world!')).toBeInTheDocument();
    
    // Check that input is cleared
    expect(input).toHaveValue('');
    
    // Wait for AI response
    await waitFor(() => {
      expect(screen.getByText('This is a simulated response. The AI integration will be implemented next.')).toBeInTheDocument();
    }, { timeout: 2000 });
  });

  it('shows loading state while waiting for response', async () => {
    render(<ChatWindow />);
    
    const input = screen.getByPlaceholderText('Ask anything...');
    const sendButton = screen.getByRole('button');
    
    // Send a message
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    // Check for loading dots (they should appear briefly)
    await waitFor(() => {
      const loadingDots = screen.getAllByText('', { selector: '.animate-bounce' });
      expect(loadingDots.length).toBeGreaterThan(0);
    }, { timeout: 100 });
  });
});
