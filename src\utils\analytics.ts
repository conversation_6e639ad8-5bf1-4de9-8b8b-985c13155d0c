// Type definitions for Google Analytics
declare global {
  interface Window {
    dataLayer: any[];
    gtag: (...args: any[]) => void;
  }
}

// Google Analytics 4 Measurement ID
const GA_MEASUREMENT_ID = 'G-XXXXXXXXXX'; // Replace with your actual GA4 Measurement ID

// Initialize Google Analytics
export const initGA = () => {
  // Only run in browser environment
  if (typeof window === 'undefined') return;
  
  try {
    // Load the Google Analytics script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: any[]) {
      window.dataLayer.push(arguments);
    }
    gtag('js', new Date());
    gtag('config', GA_MEASUREMENT_ID);

    // Make gtag available globally
    window.gtag = gtag;
  } catch (error) {
    console.error('Failed to initialize Google Analytics:', error);
  }
};

// Track page views
export const trackPageView = (path: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: path,
    });
  }
};

// Track custom events
export const trackEvent = (
  eventName: string,
  eventParams?: {
    [key: string]: any;
  }
) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, eventParams);
  }
};

// Track 404 errors
export const track404Error = (path: string) => {
  trackEvent('404_error', {
    page_path: path,
    timestamp: new Date().toISOString(),
  });
};

// Track user interactions
export const trackUserInteraction = (
  interactionType: string,
  elementId: string,
  elementType: string
) => {
  trackEvent('user_interaction', {
    interaction_type: interactionType,
    element_id: elementId,
    element_type: elementType,
    timestamp: new Date().toISOString(),
  });
};

// Track performance metrics
export const trackPerformance = (metric: {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
}) => {
  trackEvent('performance_metric', {
    metric_name: metric.name,
    metric_value: metric.value,
    metric_rating: metric.rating,
    timestamp: new Date().toISOString(),
  });
}; 
